/**
 * Receipt-Video Mapping Service
 * 
 * This service manages the mapping between receipt numbers and their associated video files.
 * It creates a complete audit trail linking each receipt number to the specific videos
 * uploaded during that user's session.
 * 
 * Features:
 * - Stores receipt-video mappings in AWS S3 at s3://icudatasetphrasesfortesting/receipt-numbers/
 * - Uses simple JSON format for the log file (receipt-log.json)
 * - Retroactively assigns receipt numbers to existing videos
 * - Links new videos to receipt numbers during upload process
 * - Maintains sequential 6-digit receipt numbering (000001, 000002, etc.)
 */

import { S3Client, GetObjectCommand, PutObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { fromCognitoIdentityPool } from '@aws-sdk/credential-provider-cognito-identity';
import { CognitoIdentityClient } from '@aws-sdk/client-cognito-identity';

// AWS Configuration
const AWS_REGION = process.env.REACT_APP_AWS_REGION || 'ap-southeast-2';
const BUCKET_NAME = process.env.REACT_APP_S3_BUCKET || 'icudatasetphrasesfortesting';
const IDENTITY_POOL_ID = process.env.REACT_APP_AWS_IDENTITY_POOL_ID;

// Receipt log configuration
const RECEIPT_LOG_KEY = 'receipt-numbers/receipt-log.json';
const RECEIPT_BACKUP_KEY = 'receipt-numbers/receipt-log-backup.json';

// Initialize AWS clients
let s3Client = null;

const initializeAWS = () => {
  if (!s3Client && IDENTITY_POOL_ID) {
    try {
      s3Client = new S3Client({
        region: AWS_REGION,
        credentials: fromCognitoIdentityPool({
          client: new CognitoIdentityClient({ region: AWS_REGION }),
          identityPoolId: IDENTITY_POOL_ID,
        }),
      });
      console.log('✅ Receipt Mapping Service: AWS S3 client initialized');
    } catch (error) {
      console.error('❌ Receipt Mapping Service: Failed to initialize AWS:', error);
    }
  }
};

// Initialize on module load
initializeAWS();

/**
 * Receipt Mapping Service
 */
export const receiptMappingService = {
  
  /**
   * Get the current receipt log from S3
   * @returns {Promise<Object>} Receipt log object
   */
  async getReceiptLog() {
    try {
      console.log('📋 Fetching receipt log from S3...');
      
      if (!s3Client) {
        console.warn('⚠️ S3 client not initialized, returning empty log');
        return {};
      }

      const command = new GetObjectCommand({
        Bucket: BUCKET_NAME,
        Key: RECEIPT_LOG_KEY
      });

      const response = await s3Client.send(command);
      const logData = await response.Body.transformToString();
      const receiptLog = JSON.parse(logData);
      
      console.log('✅ Receipt log fetched successfully:', Object.keys(receiptLog).length, 'receipts');
      return receiptLog;
      
    } catch (error) {
      if (error.name === 'NoSuchKey') {
        console.log('📋 No existing receipt log found, creating new one');
        return {};
      }
      console.error('❌ Error fetching receipt log:', error);
      return {};
    }
  },

  /**
   * Save the receipt log to S3
   * @param {Object} receiptLog - The receipt log object to save
   * @returns {Promise<boolean>} Success status
   */
  async saveReceiptLog(receiptLog) {
    try {
      console.log('💾 Saving receipt log to S3...');
      
      if (!s3Client) {
        console.warn('⚠️ S3 client not initialized, cannot save receipt log');
        return false;
      }

      // Create backup first
      try {
        const backupCommand = new PutObjectCommand({
          Bucket: BUCKET_NAME,
          Key: RECEIPT_BACKUP_KEY,
          Body: JSON.stringify(receiptLog, null, 2),
          ContentType: 'application/json',
          Metadata: {
            'backup-timestamp': new Date().toISOString(),
            'total-receipts': Object.keys(receiptLog).length.toString()
          }
        });
        await s3Client.send(backupCommand);
        console.log('✅ Receipt log backup created');
      } catch (backupError) {
        console.warn('⚠️ Failed to create backup, continuing with main save:', backupError);
      }

      // Save main receipt log
      const command = new PutObjectCommand({
        Bucket: BUCKET_NAME,
        Key: RECEIPT_LOG_KEY,
        Body: JSON.stringify(receiptLog, null, 2),
        ContentType: 'application/json',
        Metadata: {
          'last-updated': new Date().toISOString(),
          'total-receipts': Object.keys(receiptLog).length.toString(),
          'service-version': '1.0.0'
        }
      });

      await s3Client.send(command);
      console.log('✅ Receipt log saved successfully');
      return true;
      
    } catch (error) {
      console.error('❌ Error saving receipt log:', error);
      return false;
    }
  },

  /**
   * Scan existing videos in S3 and assign them to receipt 000001
   * @returns {Promise<Object>} Assignment result
   */
  async assignReceiptToExistingVideos() {
    try {
      console.log('🔍 Scanning existing videos for receipt assignment...');
      
      if (!s3Client) {
        console.warn('⚠️ S3 client not initialized, cannot scan videos');
        return { success: false, error: 'S3 client not initialized' };
      }

      // Scan the specific path for existing videos
      const scanPath = 'icu-videos/18to39/male/mixed/';
      const listCommand = new ListObjectsV2Command({
        Bucket: BUCKET_NAME,
        Prefix: scanPath,
        MaxKeys: 1000
      });

      const response = await s3Client.send(listCommand);
      const videoFiles = (response.Contents || [])
        .filter(obj => obj.Key.endsWith('.webm') || obj.Key.endsWith('.mp4'))
        .map(obj => `s3://${BUCKET_NAME}/${obj.Key}`);

      console.log(`📹 Found ${videoFiles.length} existing video files`);

      if (videoFiles.length === 0) {
        console.log('📋 No existing videos found to assign');
        return { success: true, videosAssigned: 0, receiptNumber: null };
      }

      // Get current receipt log
      const receiptLog = await this.getReceiptLog();

      // Check if receipt 000001 already exists
      if (receiptLog['000001']) {
        console.log('📋 Receipt 000001 already exists, skipping assignment');
        return { 
          success: true, 
          videosAssigned: receiptLog['000001'].videos.length,
          receiptNumber: '000001',
          alreadyExists: true 
        };
      }

      // Create receipt 000001 entry
      const receiptEntry = {
        timestamp: new Date().toISOString(),
        videos: videoFiles,
        demographics: {
          age: '18to39',
          gender: 'male',
          ethnicity: 'mixed'
        },
        sessionId: 'useruser01', // Extracted from filename pattern
        assignmentType: 'retroactive',
        assignedAt: new Date().toISOString()
      };

      receiptLog['000001'] = receiptEntry;

      // Save updated receipt log
      const saveSuccess = await this.saveReceiptLog(receiptLog);
      
      if (saveSuccess) {
        // Update localStorage to ensure next receipt will be 000002
        localStorage.setItem('icuAppReceiptCounter', '1');
        console.log('✅ Receipt 000001 assigned to existing videos');
        console.log('🔢 Receipt counter set to 1 (next receipt will be 000002)');
        
        return {
          success: true,
          videosAssigned: videoFiles.length,
          receiptNumber: '000001',
          videos: videoFiles
        };
      } else {
        throw new Error('Failed to save receipt log');
      }
      
    } catch (error) {
      console.error('❌ Error assigning receipt to existing videos:', error);
      return { success: false, error: error.message };
    }
  },

  /**
   * Add a new receipt-video mapping for a completed session
   * @param {string} receiptNumber - The receipt number (e.g., '000002')
   * @param {Array} videoUrls - Array of S3 URLs for the videos
   * @param {Object} demographics - User demographic information
   * @param {string} sessionId - Session identifier
   * @returns {Promise<boolean>} Success status
   */
  async addReceiptMapping(receiptNumber, videoUrls, demographics, sessionId) {
    try {
      console.log(`📋 Adding receipt mapping for ${receiptNumber}...`);
      console.log(`📹 Videos: ${videoUrls.length} files`);
      
      // Get current receipt log
      const receiptLog = await this.getReceiptLog();

      // Create new receipt entry
      const receiptEntry = {
        timestamp: new Date().toISOString(),
        videos: videoUrls,
        demographics: {
          age: demographics.ageGroup || demographics.age,
          gender: demographics.gender,
          ethnicity: demographics.ethnicity
        },
        sessionId: sessionId || demographics.userId || 'anonymous',
        assignmentType: 'prospective',
        recordingCount: videoUrls.length
      };

      receiptLog[receiptNumber] = receiptEntry;

      // Save updated receipt log
      const saveSuccess = await this.saveReceiptLog(receiptLog);
      
      if (saveSuccess) {
        console.log(`✅ Receipt ${receiptNumber} mapping saved successfully`);
        return true;
      } else {
        throw new Error('Failed to save receipt log');
      }
      
    } catch (error) {
      console.error(`❌ Error adding receipt mapping for ${receiptNumber}:`, error);
      return false;
    }
  },

  /**
   * Get videos associated with a specific receipt number
   * @param {string} receiptNumber - The receipt number to look up
   * @returns {Promise<Object|null>} Receipt data or null if not found
   */
  async getReceiptVideos(receiptNumber) {
    try {
      const receiptLog = await this.getReceiptLog();
      return receiptLog[receiptNumber] || null;
    } catch (error) {
      console.error(`❌ Error getting videos for receipt ${receiptNumber}:`, error);
      return null;
    }
  },

  /**
   * Get all receipt mappings
   * @returns {Promise<Object>} Complete receipt log
   */
  async getAllReceiptMappings() {
    return await this.getReceiptLog();
  },

  /**
   * Initialize the receipt mapping system
   * This should be called once when the application starts
   * @returns {Promise<Object>} Initialization result
   */
  async initialize() {
    try {
      console.log('🚀 Initializing Receipt Mapping Service...');
      
      // Check if we can connect to S3
      if (!s3Client) {
        console.warn('⚠️ S3 client not available, receipt mapping will be limited');
        return { success: false, error: 'S3 client not initialized' };
      }

      // Try to fetch existing receipt log
      const receiptLog = await this.getReceiptLog();
      const existingReceipts = Object.keys(receiptLog).length;
      
      console.log(`📋 Found ${existingReceipts} existing receipts`);

      // If no receipts exist, try to assign existing videos to receipt 000001
      if (existingReceipts === 0) {
        console.log('🔄 No existing receipts found, checking for videos to assign...');
        const assignmentResult = await this.assignReceiptToExistingVideos();
        
        return {
          success: true,
          existingReceipts: existingReceipts,
          retroactiveAssignment: assignmentResult
        };
      }

      return {
        success: true,
        existingReceipts: existingReceipts,
        retroactiveAssignment: { success: true, videosAssigned: 0, alreadyExists: true }
      };
      
    } catch (error) {
      console.error('❌ Error initializing Receipt Mapping Service:', error);
      return { success: false, error: error.message };
    }
  }
};

export default receiptMappingService;
