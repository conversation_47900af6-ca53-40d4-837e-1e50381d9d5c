import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Divider
} from '@mui/material';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { receiptMappingService } from '../services/receiptMappingService';

/**
 * Component for generating and displaying a receipt for the user's recording session
 * Now includes receipt-video mapping functionality
 */
const ReceiptGenerator = ({ demographicInfo, sessionRecordingsCount, onClose, savedVideos = [] }) => {
  const [copied, setCopied] = useState(false);
  const [receiptMappingStatus, setReceiptMappingStatus] = useState('pending');
  
  // Generate a simple 6-digit sequential receipt number
  const generateReceiptNumber = () => {
    try {
      // Get current receipt counter from localStorage
      const currentCounter = parseInt(localStorage.getItem('icuAppReceiptCounter') || '0', 10);
      const nextCounter = currentCounter + 1;

      // Store updated counter
      localStorage.setItem('icuAppReceiptCounter', nextCounter.toString());

      // Format as 6-digit number with leading zeros
      return nextCounter.toString().padStart(6, '0');
    } catch (error) {
      console.warn('Error generating receipt number:', error);
      // Fallback to timestamp-based number
      return Date.now().toString().slice(-6);
    }
  };
  
  // Generate receipt number once when component mounts
  const receiptNumber = React.useMemo(generateReceiptNumber, []);

  // Handle receipt-video mapping when component mounts
  useEffect(() => {
    const createReceiptMapping = async () => {
      if (savedVideos && savedVideos.length > 0) {
        console.log(`🧾 Creating receipt mapping for ${receiptNumber} with ${savedVideos.length} videos`);
        setReceiptMappingStatus('creating');

        try {
          // Extract video URLs from saved videos
          const videoUrls = savedVideos.map(video => video.url || video.key || video.filename);
          const sessionId = demographicInfo?.userId || `session_${Date.now()}`;

          const success = await receiptMappingService.addReceiptMapping(
            receiptNumber,
            videoUrls,
            demographicInfo,
            sessionId
          );

          if (success) {
            console.log(`✅ Receipt mapping created successfully for ${receiptNumber}`);
            setReceiptMappingStatus('success');
          } else {
            console.warn(`⚠️ Failed to create receipt mapping for ${receiptNumber}`);
            setReceiptMappingStatus('failed');
          }
        } catch (error) {
          console.error('❌ Error creating receipt mapping:', error);
          setReceiptMappingStatus('failed');
        }
      } else {
        console.log('📋 No saved videos provided, skipping receipt mapping');
        setReceiptMappingStatus('skipped');
      }
    };

    createReceiptMapping();
  }, [receiptNumber, savedVideos, demographicInfo]);

  // Handle copy to clipboard
  const handleCopy = () => {
    navigator.clipboard.writeText(receiptNumber);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  
  return (
    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 3 }}>
      <Paper 
        elevation={3} 
        sx={{ 
          p: 4, 
          maxWidth: 500, 
          width: '100%',
          borderRadius: 2,
          border: '1px solid #e0e0e0'
        }}
        id="receipt-content"
      >
        <Typography variant="h4" align="center" gutterBottom sx={{ color: '#009688', fontWeight: 'bold' }}>
          Thank you for your contribution!
        </Typography>

        <Typography variant="body1" paragraph align="center" sx={{ mb: 4 }}>
          Your recordings have been successfully saved and will help train AI technology to give a voice to those who need it most.
        </Typography>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ textAlign: 'center', my: 4 }}>
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#00796b', mb: 2 }}>
            Receipt Number
          </Typography>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2 }}>
            <Typography variant="h3" sx={{ fontFamily: 'monospace', fontWeight: 'bold', color: '#263238' }}>
              {receiptNumber}
            </Typography>
            <IconButton
              onClick={handleCopy}
              color={copied ? "success" : "primary"}
              size="large"
              sx={{ ml: 1 }}
            >
              {copied ? <CheckCircleIcon /> : <ContentCopyIcon />}
            </IconButton>
          </Box>
          {copied && (
            <Typography variant="body2" sx={{ color: '#00796b', mt: 1 }}>
              ✅ Receipt number copied to clipboard
            </Typography>
          )}
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={onClose}
            sx={{
              fontWeight: 'bold',
              py: 2,
              px: 6,
              fontSize: '1.1rem'
            }}
          >
            Continue
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default ReceiptGenerator;
